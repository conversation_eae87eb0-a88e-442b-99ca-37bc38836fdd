# 项目架构设计文档

## 1. 概述

本项目是一个基于Spring Boot的照片上传下载系统，提供完整的文件存储解决方案。本文档描述了系统的整体架构设计。

## 2. 架构图

```
+---------------------+
|      客户端/用户       |
+----------+----------+
           |
+----------v----------+     +------------------+
|   Spring Boot Web    |<--->|   Swagger UI     |
|    (Controller)      |     | (API 文档)       |
+----------+----------+     +------------------+
           |
+----------v----------+     +------------------+
|     Service Layer    |<--->|  Metrics监控     |
| (FileStorageService) |     |  & 健康检查      |
+----------+----------+     +------------------+
           |
+----------v----------+     +------------------+
|      Repository      |<--->|    数据库（JPA）  |
|  (FileInfoRepository) |     | (H2/MySQL/PG)    |
+----------+----------+     +------------------+
           |
+----------v----------+     +------------------+
|     文件存储服务       |<--->|  文件系统存储     |
| (本地文件系统/S3等)   |     |  或云存储服务     |
+---------------------+     +------------------+
```

## 3. 分层架构

### 3.1 表示层（Presentation Layer）
- RESTful API控制器
- 异常处理
- 请求/响应DTO
- Swagger API文档
- 在线预览模板

### 3.2 业务逻辑层（Business Logic Layer）
- 文件上传下载服务接口
- 文件验证和安全处理
- 文件名生成和管理
- 上传进度跟踪
- 图片压缩处理
- 存储健康检查
- 自动清理任务

### 3.3 数据访问层（Data Access Layer）
- JPA实体类（FileInfo）
- 数据库操作接口（FileInfoRepository）
- 数据库表结构设计

### 3.4 文件存储层（File Storage Layer）
- 本地文件系统存储
- 文件读写操作
- 文件类型检测
- 文件安全处理

## 4. 核心组件

### 4.1 文件上传功能
- 支持单文件和多文件上传
- 实现文件类型验证（仅允许图片格式）
- 文件大小限制
- 自动生成唯一文件名
- 上传进度显示

### 4.2 文件下载功能
- 文件下载接口
- 支持断点续传
- 在线预览功能
- 访问权限控制

### 4.3 安全特性
- 文件类型安全检查
- 文件名安全处理
- 访问权限控制
- 防盗链措施
- XSS防护

### 4.4 性能优化
- 文件上传进度监控
- 大文件处理优化
- 图片压缩功能
- 缓存机制

### 4.5 监控与维护
- 文件存储健康检查
- 存储容量监控
- 定期清理机制
- 指标收集（Micrometer）

## 5. 技术选型

### 5.1 主框架
- Spring Boot 最新稳定版本
- Spring Web
- Spring Data JPA
- Spring Security
- Spring Actuator

### 5.2 辅助工具
- Swagger2 - API文档
- Thumbnailator - 图片处理
- Lombok - 简化代码
- Micrometer - 指标收集
- H2数据库 - 开发环境

## 6. 配置管理

采用分层配置管理策略：

```properties
# 基础存储配置
file.storage.upload-dir=d:/uploads
file.storage.allowed-extensions=.jpg,.jpeg,.png,.gif
file.storage.max-size=10485760

# 文件清理配置
file.cleanup.enabled=true
file.cleanup.days-to-keep=30
file.cleanup.storage-usage-warning-threshold=80.0

# 图片压缩配置
image.compress.enabled=true
image.compress.quality=0.7
image.compress.width=800
image.compress.height=600

# 上传进度跟踪配置
file.upload-progress.enabled=true
file.upload-progress.retention-time-minutes=30

# 文件存储健康检查配置
management.health.file-storage.enabled=true
management.health.file-storage.warning-threshold-percentage=80.0
management.health.file-storage.critical-threshold-percentage=90.0
endpoint.health.file-storage.enabled=true
endpoint.health.file-storage.path=/actuator/health/storage
```

## 7. 安全设计

### 7.1 认证与授权
- 基于Spring Security的认证体系
- 角色基础的访问控制
- CSRF保护
- XSS防护

### 7.2 文件安全
- 文件类型白名单验证
- 文件名安全处理（防路径穿越攻击）
- 文件存储目录隔离
- 访问URL签名验证（可选）

### 7.3 接口安全
- 请求参数验证
- 请求大小限制
- 防暴力破解（上传进度跟踪）
- 日志审计