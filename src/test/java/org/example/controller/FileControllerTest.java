package org.example.controller;

import org.example.dto.FileResponse;
import org.example.entity.FileInfo;
import org.example.service.FileStorageService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

import java.time.LocalDateTime;
import java.util.Collections;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@SpringBootTest
@AutoConfigureMockMvc
public class FileControllerTest {
    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private FileStorageService fileStorageService;

    private FileInfo testFileInfo;

    @BeforeEach
    void setUp() {
        testFileInfo = new FileInfo();
        testFileInfo.setId(1L);
        testFileInfo.setFileName("test.jpg");
        testFileInfo.setOriginalName("test.jpg");
        testFileInfo.setFilePath("/tmp/test.jpg");
        testFileInfo.setFileSize(1024L);
        testFileInfo.setFileType("image/jpeg");
        testFileInfo.setUploadTime(LocalDateTime.now());
    }

    @Test
    void testUploadFile() throws Exception {
        when(fileStorageService.storeFile(any(), anyString(), anyBoolean()))
                .thenReturn(testFileInfo);

        mockMvc.perform(MockMvcRequestBuilders.multipart("/api/files/upload")
                .file("file", "test content".getBytes())
                .param("accessRole", "ROLE_USER")
                .param("isPublic", "false")
                .accept(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.fileName").value("test.jpg"));
    }

    @Test
    void testDownloadFile() throws Exception {
        when(fileStorageService.getFileForDownload(anyString(), anyString()))
                .thenReturn("test content".getBytes());

        mockMvc.perform(MockMvcRequestBuilders.get("/api/files/download/test.jpg")
                .header("Range", "bytes=0-10"))
                .andExpect(MockMvcResultMatchers.status().isPartialContent())
                .andExpect(MockMvcResultMatchers.header().exists(HttpHeaders.CONTENT_RANGE));
    }

    @Test
    void testGetFileInfo() throws Exception {
        when(fileStorageService.getFileByFileName(anyString()))
                .thenReturn(testFileInfo);

        mockMvc.perform(MockMvcRequestBuilders.get("/api/files/test.jpg")
                .accept(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.jsonPath("$.originalName").value("test.jpg"));
    }

    @Test
    void testDeleteFile() throws Exception {
        Mockito.doNothing().when(fileStorageService).deleteFile(anyString());

        mockMvc.perform(MockMvcRequestBuilders.delete("/api/files/test.jpg")
                .accept(MediaType.APPLICATION_JSON))
                .andExpect(MockMvcResultMatchers.status().isOk());
    }

    @Test
    void testPreviewFile() throws Exception {
        when(fileStorageService.getFileForDownload(anyString(), anyString()))
                .thenReturn("test content".getBytes());

        mockMvc.perform(MockMvcRequestBuilders.get("/api/files/preview/test.jpg")
                .accept(MediaType.IMAGE_JPEG_VALUE))
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andExpect(MockMvcResultMatchers.content().contentType(MediaType.IMAGE_JPEG));
    }
}