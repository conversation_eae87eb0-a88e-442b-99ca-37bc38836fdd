package org.example.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConditionalOnProperty(prefix = "management.health.file-storage", name = "enabled", havingValue = "true")
public class FileStorageHealthAutoConfiguration {
    @Bean
    public FileStorageHealthIndicator fileStorageHealthIndicator(
            FileProperties fileProperties,
            FileStorageHealthProperties fileStorageHealthProperties) {
        return new FileStorageHealthIndicator(fileProperties, fileStorageHealthProperties);
    }

    @Bean
    @ConditionalOnProperty(prefix = "endpoint.health.file-storage", name = "enabled", havingValue = "true")
    public FileStorageHealthIndicatorEndpoint fileStorageHealthIndicatorEndpoint(
            FileStorageHealthEndpointProperties fileStorageHealthEndpointProperties,
            FileStorageHealthProperties fileStorageHealthProperties) {
        return new FileStorageHealthIndicatorEndpoint(fileStorageHealthEndpointProperties, 
                fileStorageHealthProperties);
    }
}