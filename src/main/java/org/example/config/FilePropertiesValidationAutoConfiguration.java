package org.example.config;

import org.springframework.boot.context.properties.bind.validation.ValidationErrors;
import org.springframework.boot.context.properties.bind.validation.ValidatorAdapter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class FilePropertiesValidationAutoConfiguration {
    @Bean
    public ValidatorAdapter<FileProperties> filePropertiesValidatorAdapter(
            FileStoragePropertiesValidator fileStoragePropertiesValidator,
            FileCleanupPropertiesValidator fileCleanupPropertiesValidator,
            ImageCompressionPropertiesValidator imageCompressionPropertiesValidator,
            UploadProgressPropertiesValidator uploadProgressPropertiesValidator) {
        return new ValidatorAdapter<>() {
            @Override
            public void validate(FileProperties properties, ValidationErrors errors) {
                // 验证文件存储配置
                fileStoragePropertiesValidator.validate(properties, errors);
                
                // 验证文件清理配置
                fileCleanupPropertiesValidator.validate(properties, errors);
                
                // 验证图片压缩配置
                imageCompressionPropertiesValidator.validate(properties.getImageCompression(), errors);
                
                // 验证上传进度配置
                uploadProgressPropertiesValidator.validate(properties.getUploadProgress(), errors);
            }

            @Override
            public Class<FileProperties> getValidatedType() {
                return FileProperties.class;
            }
        };
    }
}