package org.example.config;

import org.springframework.boot.context.properties.bind.validation.ValidationErrors;
import org.springframework.stereotype.Component;

@Component
public class FileStoragePropertiesValidator {
    public void validate(FileProperties fileProperties, ValidationErrors errors) {
        // 验证上传目录是否可写
        if (fileProperties.getStorage().getUploadDir() == null || fileProperties.getStorage().getUploadDir().isEmpty()) {
            errors.reject("uploadDir", "文件存储目录不能为空");
        } else {
            try {
                Path uploadPath = Paths.get(fileProperties.getStorage().getUploadDir());
                
                // 如果目录不存在，尝试创建它
                if (!Files.exists(uploadPath)) {
                    // 这里只是检查父目录是否存在并可写
                    Path parentDir = uploadPath.getParent();
                    if (parentDir != null && !Files.exists(parentDir)) {
                        errors.reject("uploadDir", "文件存储目录的父目录不存在: " + parentDir);
                    }
                    
                    if (!Files.isDirectory(parentDir)) {
                        errors.reject("uploadDir", "文件存储目录的父路径不是一个目录: " + parentDir);
                    }
                    
                    if (!Files.isWritable(parentDir)) {
                        errors.reject("uploadDir", "文件存储目录的父目录不可写: " + parentDir);
                    }
                } else {
                    if (!Files.isDirectory(uploadPath)) {
                        errors.reject("uploadDir", "指定的文件存储路径不是一个目录: " + uploadPath);
                    }
                    
                    if (!Files.isReadable(uploadPath)) {
                        errors.reject("uploadDir", "文件存储目录不可读: " + uploadPath);
                    }
                    
                    if (!Files.isWritable(uploadPath)) {
                        errors.reject("uploadDir", "文件存储目录不可写: " + uploadPath);
                    }
                }
            } catch (Exception e) {
                errors.reject("uploadDir", "无法访问文件存储目录: " + fileProperties.getStorage().getUploadDir());
            }
        }
        
        // 验证允许的扩展名格式
        if (fileProperties.getStorage().getAllowedExtensions() == null || 
            fileProperties.getStorage().getAllowedExtensions().isEmpty()) {
            errors.reject("allowedExtensions", "允许的文件扩展名不能为空");
        } else {
            String[] extensions = fileProperties.getStorage().getAllowedExtensions().split(",");
            for (String ext : extensions) {
                if (!ext.startsWith(".")) {
                    errors.reject("allowedExtensions", "文件扩展名必须以点开头: " + ext);
                }
            }
        }
        
        // 验证最大文件大小
        if (fileProperties.getStorage().getMaxSize() <= 0) {
            errors.reject("maxSize", "最大文件大小必须大于0");
        }
    }
}