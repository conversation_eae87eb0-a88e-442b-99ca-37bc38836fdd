package org.example.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;

@Configuration
public class CorsConfig {
    @Bean
    public CorsFilter corsFilter() {
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        CorsConfiguration config = new CorsConfiguration();
        
        // 允许的源
        config.addAllowedOrigin("*");
        // 允许的方法
        config.addAllowedMethod("*");
        // 允许的头信息
        config.addAllowedHeader("*");
        // 暴露的头信息
        config.addExposedHeader("X-Upload-ID");
        // 是否允许凭证
        config.setAllowCredentials(false);
        // 预检请求的有效期
        config.setMaxAge(3600);
        
        source.registerCorsConfiguration("/api/**", config);
        return new CorsFilter(source);
    }
}