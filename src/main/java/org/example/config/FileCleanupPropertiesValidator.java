package org.example.config;

import org.springframework.boot.context.properties.bind.validation.ValidationErrors;
import org.springframework.stereotype.Component;

@Component
public class FileCleanupPropertiesValidator {
    public void validate(FileProperties fileProperties, ValidationErrors errors) {
        // 验证文件保留天数
        if (fileProperties.getCleanup().getDaysToKeep() <= 0) {
            errors.reject("daysToKeep", "文件保留天数必须大于0");
        }
        
        // 验证存储使用率警告阈值
        if (fileProperties.getCleanup().getStorageUsageWarningThreshold() < 0 || 
            fileProperties.getCleanup().getStorageUsageWarningThreshold() > 100) {
            errors.reject("storageUsageWarningThreshold", "存储使用率警告阈值必须在0到100之间");
        }
    }
}