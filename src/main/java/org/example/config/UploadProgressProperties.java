package org.example.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "file.upload-progress")
public class UploadProgressProperties {
    /**
     * 是否启用上传进度跟踪
     */
    private boolean enabled = true;

    /**
     * 上传进度信息保留时间（分钟）
     */
    private int retentionTimeMinutes = 30;
}