package org.example.config;

import org.example.util.FileNameSanitizer;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import java.nio.file.Path;
import java.nio.file.Paths;

@Component
public class FileStorageInitializer implements ApplicationListener<ContextRefreshedEvent> {
    private final FileProperties fileProperties;

    public FileStorageInitializer(FileProperties fileProperties) {
        this.fileProperties = fileProperties;
    }

    @Override
    public void onApplicationEvent(ContextRefreshedEvent event) {
        // 验证路径安全性
        if (!FileNameSanitizer.isSafePath(fileProperties.getStorage().getUploadDir())) {
            throw new IllegalStateException("配置的上传目录不在安全路径内: " + fileProperties.getStorage().getUploadDir());
        }
        
        try {
            Path uploadPath = Paths.get(fileProperties.getStorage().getUploadDir());
            // 创建存储目录（如果不存在）
            if (!uploadPath.toFile().exists()) {
                uploadPath.toFile().mkdirs();
            }
            
            // 创建临时目录（如果不存在）
            Path tempPath = Paths.get(System.getProperty("java.io.tmpdir"), "file-upload-tmp");
            if (!tempPath.toFile().exists()) {
                tempPath.toFile().mkdirs();
            }
        } catch (Exception e) {
            throw new IllegalStateException("无法初始化文件存储目录", e);
        }
    }
}