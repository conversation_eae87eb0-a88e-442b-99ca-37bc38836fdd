package org.example.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "swagger")
public class SwaggerProperties {
    /**
     * 是否启用Swagger
     */
    private boolean enabled = true;

    /**
     * 标题
     */
    private String title = "照片上传下载系统 API 文档";

    /**
     * 描述
     */
    private String description = "提供照片上传、下载、预览等核心功能的RESTful API";

    /**
     * 版本
     */
    private String version = "1.0.0";

    /**
     * 许可证
     */
    private String license = "Apache License 2.0";

    /**
     * 许可证URL
     */
    private String licenseUrl = "https://www.apache.org/licenses/LICENSE-2.0.html";

    /**
     * 联系人名称
     */
    private String contactName = "API Team";

    /**
     * 联系人网址
     */
    private String contactUrl = "http://example.com";

    /**
     * 联系人邮箱
     */
    private String contactEmail = "<EMAIL>";
}