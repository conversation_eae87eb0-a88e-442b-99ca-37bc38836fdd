package org.example.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "management.health.file-storage")
public class FileStorageHealthProperties {
    /**
     * 是否启用文件存储健康检查
     */
    private boolean enabled = true;

    /**
     * 存储使用率警告阈值（百分比）
     */
    private double warningThresholdPercentage = 80.0;

    /**
     * 存储使用率临界阈值（百分比）
     */
    private double criticalThresholdPercentage = 90.0;
}