package org.example.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "image.compress")
public class ImageCompressionProperties {
    private boolean enabled = true;
    private double quality = 0.7;
    private int width = 800;
    private int height = 600;
}