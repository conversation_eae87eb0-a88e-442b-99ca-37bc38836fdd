package org.example.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "file")
public class FileProperties {
    /**
     * 文件存储相关配置
     */
    private FileStorageProperties storage = new FileStorageProperties();

    /**
     * 文件清理相关配置
     */
    private FileCleanupProperties cleanup = new FileCleanupProperties();
}