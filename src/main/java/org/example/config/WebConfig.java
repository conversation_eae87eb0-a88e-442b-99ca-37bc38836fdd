package org.example.config;

import org.example.interceptor.UploadProgressInterceptor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class WebConfig implements WebMvcConfigurer {
    private final UploadProgressInterceptor uploadProgressInterceptor;

    public WebConfig(UploadProgressInterceptor uploadProgressInterceptor) {
        this.uploadProgressInterceptor = uploadProgressInterceptor;
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(uploadProgressInterceptor)
                .addPathPatterns("/api/files/upload", "/api/files/uploads");
    }
}