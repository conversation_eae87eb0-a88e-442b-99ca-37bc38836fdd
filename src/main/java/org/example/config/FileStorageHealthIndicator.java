package org.example.config;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.binder.MeterBinder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.nio.file.*;

@Component
@Slf4j
public class FileStorageHealthIndicator implements MeterBinder {
    private final FileProperties fileProperties;
    private final FileStorageHealthProperties fileStorageHealthProperties;

    public FileStorageHealthIndicator(FileProperties fileProperties, 
                                     FileStorageHealthProperties fileStorageHealthProperties) {
        this.fileProperties = fileProperties;
        this.fileStorageHealthProperties = fileStorageHealthProperties;
    }

    @Override
    public void bindTo(MeterRegistry registry) {
        try {
            Path uploadPath = Paths.get(fileProperties.getStorage().getUploadDir());
            
            // 文件系统使用率
            FileStore fileStore = Files.getFileStore(uploadPath);
            double usedPercentage = (double) (fileStore.getTotalSpace() - fileStore.getUsableSpace()) / 
                    fileStore.getTotalSpace() * 100;
            registry.gauge("file.storage.usage.percent", (double) Math.round(usedPercentage * 100) / 100);
            
            // 文件数量
            long fileCount = 0;
            if (Files.exists(uploadPath)) {
                fileCount = Files.list(uploadPath).count();
            }
            registry.gauge("file.storage.count", fileCount);
            
            // 存储空间剩余百分比
            double freePercentage = (double) fileStore.getUsableSpace() / fileStore.getTotalSpace() * 100;
            registry.gauge("file.storage.free.percent", (double) Math.round(freePercentage * 100) / 100);
            
            // 存储状态
            if (usedPercentage > fileStorageHealthProperties.getCriticalThresholdPercentage()) {
                registry.gauge("file.storage.status", 2); // Critical
            } else if (usedPercentage > fileStorageHealthProperties.getWarningThresholdPercentage()) {
                registry.gauge("file.storage.status", 1); // Warning
            } else {
                registry.gauge("file.storage.status", 0); // OK
            }
            
        } catch (Exception e) {
            log.error("无法收集文件存储健康指标", e);
        }
    }
}