package org.example.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConditionalOnProperty(prefix = "file", name = "enabled", havingValue = "true", matchIfMissing = true)
public class FileStorageAutoConfiguration {
    @Bean
    @ConditionalOnProperty(prefix = "file.cleanup", name = "enabled", havingValue = "true")
    public FileCleanupTask fileCleanupTask(FileProperties fileProperties, FileInfoRepository fileInfoRepository) {
        return new FileCleanupTask(fileProperties, fileInfoRepository);
    }

    @Bean
    @ConditionalOnProperty(prefix = "file.upload-progress", name = "enabled", havingValue = "true")
    public UploadProgressInterceptor uploadProgressInterceptor(UploadProgressListener uploadProgressListener) {
        return new UploadProgressInterceptor(uploadProgressListener);
    }
}