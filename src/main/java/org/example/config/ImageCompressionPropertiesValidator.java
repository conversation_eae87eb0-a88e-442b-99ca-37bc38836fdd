package org.example.config;

import org.springframework.boot.context.properties.bind.validation.ValidationErrors;
import org.springframework.stereotype.Component;

@Component
public class ImageCompressionPropertiesValidator {
    public void validate(ImageCompressionProperties imageCompressionProperties, ValidationErrors errors) {
        // 验证压缩质量范围
        if (imageCompressionProperties.getQuality() < 0 || 
            imageCompressionProperties.getQuality() > 1.0) {
            errors.reject("quality", "图片压缩质量必须在0到1之间");
        }
        
        // 验证压缩尺寸
        if (imageCompressionProperties.getWidth() <= 0) {
            errors.reject("width", "图片压缩宽度必须大于0");
        }
        
        if (imageCompressionProperties.getHeight() <= 0) {
            errors.reject("height", "图片压缩高度必须大于0");
        }
    }
}