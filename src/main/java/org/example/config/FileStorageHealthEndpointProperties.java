package org.example.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "endpoint.health.file-storage")
public class FileStorageHealthEndpointProperties {
    /**
     * 是否启用文件存储健康检查端点
     */
    private boolean enabled = true;

    /**
     * 端点的路径
     */
    private String path = "/actuator/health/storage";
}