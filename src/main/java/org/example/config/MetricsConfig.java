package org.example.config;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class MetricsConfig {
    @Bean
    public Counter uploadCounter(MeterRegistry registry) {
        return Counter.builder("file.upload.count")
                .description("Total number of file uploads")
                .register(registry);
    }

    @Bean
    public Counter downloadCounter(MeterRegistry registry) {
        return Counter.builder("file.download.count")
                .description("Total number of file downloads")
                .register(registry);
    }

    @Bean
    public Counter errorCounter(MeterRegistry registry) {
        return Counter.builder("file.error.count")
                .description("Total number of errors")
                .register(registry);
    }
}