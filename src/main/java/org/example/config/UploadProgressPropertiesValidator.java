package org.example.config;

import org.springframework.boot.context.properties.bind.validation.ValidationErrors;
import org.springframework.stereotype.Component;

@Component
public class UploadProgressPropertiesValidator {
    public void validate(UploadProgressProperties uploadProgressProperties, ValidationErrors errors) {
        // 验证保留时间
        if (uploadProgressProperties.getRetentionTimeMinutes() <= 0) {
            errors.reject("retentionTimeMinutes", "上传进度信息保留时间必须大于0分钟");
        }
    }
}