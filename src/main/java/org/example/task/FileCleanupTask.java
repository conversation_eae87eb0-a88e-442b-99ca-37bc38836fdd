package org.example.task;

import lombok.extern.slf4j.Slf4j;
import org.example.config.FileProperties;
import org.example.entity.FileInfo;
import org.example.repository.FileInfoRepository;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.file.*;
import java.time.LocalDateTime;
import java.util.List;

@Component
@Slf4j
public class FileCleanupTask {
    private final FileProperties fileProperties;
    private final FileInfoRepository fileInfoRepository;

    public FileCleanupTask(FileProperties fileProperties, FileInfoRepository fileInfoRepository) {
        this.fileProperties = fileProperties;
        this.fileInfoRepository = fileInfoRepository;
    }

    // 每天凌晨1点执行
    @Scheduled(cron = "0 0 1 * * ?")
    public void cleanupOldFiles() {
        if (!fileProperties.getCleanup().isEnabled()) {
            log.info("文件清理任务已禁用");
            return;
        }
        
        log.info("开始执行文件清理任务，清理{}天前的文件", fileProperties.getCleanup().getDaysToKeep());
        
        try {
            // 获取过期时间
            LocalDateTime expirationTime = LocalDateTime.now().minusDays(fileProperties.getCleanup().getDaysToKeep());
            
            // 查询所有过期文件
            List<FileInfo> oldFiles = fileInfoRepository.findByUploadTimeBefore(expirationTime);
            
            if (oldFiles.isEmpty()) {
                log.info("没有需要清理的旧文件");
                return;
            }
            
            // 删除文件和记录
            for (FileInfo fileInfo : oldFiles) {
                Path filePath = Paths.get(fileInfo.getFilePath());
                if (Files.exists(filePath)) {
                    Files.delete(filePath);
                    log.info("已删除过期文件: {}", fileInfo.getFileName());
                }
                fileInfoRepository.deleteById(fileInfo.getId());
            }
            
            log.info("文件清理任务完成，共删除{}个文件", oldFiles.size());
        } catch (IOException | DirectoryIteratorException ex) {
            log.error("文件清理任务执行失败", ex);
        }
    }

    // 每小时执行一次存储监控
    @Scheduled(fixedRate = 3600000)
    public void checkStorageUsage() {
        try {
            Path uploadPath = Paths.get(fileProperties.getStorage().getUploadDir());
            if (!Files.exists(uploadPath)) {
                log.warn("文件存储目录不存在");
                return;
            }
            
            // 获取存储使用情况
            long totalSize = 0;
            long fileCount = 0;
            
            try (DirectoryStream<Path> stream = Files.newDirectoryStream(uploadPath)) {
                for (Path file : stream) {
                    if (Files.isRegularFile(file)) {
                        totalSize += Files.size(file);
                        fileCount++;
                    }
                }
            }
            
            // 获取磁盘总空间和可用空间
            FileStore fileStore = Files.getFileStore(uploadPath);
            long totalSpace = fileStore.getTotalSpace();
            long usableSpace = fileStore.getUsableSpace();
            double usedPercentage = (double) (totalSpace - usableSpace) / totalSpace * 100;
            
            log.info("当前存储使用情况: {}个文件, 总大小{}MB, 使用率{:.2f}%",
                    fileCount, totalSize / (1024 * 1024), usedPercentage);
            
            // 检查是否超过警告阈值
            if (usedPercentage > fileProperties.getCleanup().getStorageUsageWarningThreshold()) {
                log.warn("存储使用率超过警告阈值: {:.2f}%, 当前使用率: {:.2f}%", 
                        fileProperties.getCleanup().getStorageUsageWarningThreshold(), usedPercentage);
                // 这里可以添加通知管理员的逻辑
            }
            
        } catch (IOException ex) {
            log.error("存储监控任务执行失败", ex);
        }
    }
}