package org.example.endpoint;

import org.example.config.FileStorageHealthEndpointProperties;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.stereotype.Component;

import java.nio.file.*;

@Component
public class FileStorageHealthIndicatorEndpoint implements HealthIndicator {
    private final FileStorageHealthEndpointProperties fileStorageHealthEndpointProperties;
    private final FileStorageHealthProperties fileStorageHealthProperties;

    public FileStorageHealthIndicatorEndpoint(
            FileStorageHealthEndpointProperties fileStorageHealthEndpointProperties,
            FileStorageHealthProperties fileStorageHealthProperties) {
        this.fileStorageHealthEndpointProperties = fileStorageHealthEndpointProperties;
        this.fileStorageHealthProperties = fileStorageHealthProperties;
    }

    @Override
    public Health health() {
        if (!fileStorageHealthEndpointProperties.isEnabled()) {
            return Health.unknown().build();
        }
        
        try {
            Path uploadPath = Paths.get("d:/uploads");
            FileStore fileStore = Files.getFileStore(uploadPath);
            
            long totalSpace = fileStore.getTotalSpace();
            long usableSpace = fileStore.getUsableSpace();
            double usedPercentage = (double) (totalSpace - usableSpace) / totalSpace * 100;
            
            if (usedPercentage > fileStorageHealthProperties.getCriticalThresholdPercentage()) {
                return Health.outOfService()
                        .withDetail("storage.used.percent", String.format("%.2f%%", usedPercentage))
                        .withDetail("storage.free.percent", String.format("%.2f%%", (double) usableSpace / totalSpace * 100))
                        .build();
            } else if (usedPercentage > fileStorageHealthProperties.getWarningThresholdPercentage()) {
                return Health.down()
                        .withDetail("storage.used.percent", String.format("%.2f%%", usedPercentage))
                        .withDetail("storage.free.percent", String.format("%.2f%%", (double) usableSpace / totalSpace * 100))
                        .build();
            } else {
                return Health.up()
                        .withDetail("storage.used.percent", String.format("%.2f%%", usedPercentage))
                        .withDetail("storage.free.percent", String.format("%.2f%%", (double) usableSpace / totalSpace * 100))
                        .build();
            }
        } catch (Exception e) {
            return Health.down(e).build();
        }
    }
}