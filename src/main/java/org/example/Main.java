package org.example;

public class Main {
    public static void main(String[] args) {
        Calculator calculator = new Calculator();

        // 执行加法
        double resultAdd = calculator.add(10, 5);
        System.out.println("加法结果: " + resultAdd);

        // 执行减法
        double resultSubtract = calculator.subtract(10, 5);
        System.out.println("减法结果: " + resultSubtract);

        // 执行乘法
        double resultMultiply = calculator.multiply(10, 5);
        System.out.println("乘法结果: " + resultMultiply);

        // 执行除法
        try {
            double resultDivide = calculator.divide(10, 2);
            System.out.println("除法结果: " + resultDivide);
        } catch (IllegalArgumentException e) {
            System.out.println("错误: " + e.getMessage());
        }

        // 测试除以零的情况
        try {
            double resultDivideByZero = calculator.divide(10, 0);
            System.out.println("除法结果: " + resultDivideByZero);
        } catch (IllegalArgumentException e) {
            System.out.println("错误: " + e.getMessage());
        }
    }
}