package org.example.repository;

import org.example.entity.FileInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface FileInfoRepository extends JpaRepository<FileInfo, Long> {
    Optional<FileInfo> findByFileName(String fileName);
    Optional<FileInfo> findByFilePath(String filePath);
    
    @Query("SELECT f FROM FileInfo f WHERE f.uploadTime < ?1")
    List<FileInfo> findByUploadTimeBefore(LocalDateTime expirationTime);
}