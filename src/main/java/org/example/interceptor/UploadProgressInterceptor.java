package org.example.interceptor;

import org.example.listener.UploadProgressListener;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.UUID;

@Component
public class UploadProgressInterceptor implements HandlerInterceptor {
    private final UploadProgressListener uploadProgressListener;

    public UploadProgressInterceptor(UploadProgressListener uploadProgressListener) {
        this.uploadProgressListener = uploadProgressListener;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if ("POST".equalsIgnoreCase(request.getMethod()) && 
            request.getContentType() != null && 
            request.getContentType().contains("multipart/form-data")) {
            
            String uploadId = UUID.randomUUID().toString();
            request.setAttribute("uploadId", uploadId);
            
            // 将uploadId存储在RequestAttributes中，供监听器使用
            RequestContextHolder.getRequestAttributes().setAttribute(
                "uploadId", uploadId, RequestAttributes.SCOPE_REQUEST);
            
            // 设置响应头，返回uploadId给客户端
            response.setHeader("X-Upload-ID", uploadId);
        }
        
        return true;
    }
}