package org.example.service.impl;

import lombok.extern.slf4j.Slf4j;
import net.coobird.thumbnailator.Thumbnails;
import org.example.config.FileProperties;
import org.example.config.ImageCompressionProperties;
import org.example.entity.FileInfo;
import org.example.listener.UploadProgressListener;
import org.example.repository.FileInfoRepository;
import org.example.service.FileStorageService;
import org.example.util.FileValidator;
import org.example.util.FileNameSanitizer;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
@Slf4j
public class FileStorageServiceImpl implements FileStorageService {
    private final FileInfoRepository fileInfoRepository;
    private final UploadProgressListener uploadProgressListener;
    private final ImageCompressionProperties imageCompressionProperties;
    private final FileProperties fileProperties;

    public FileStorageServiceImpl(FileInfoRepository fileInfoRepository, 
                                 UploadProgressListener uploadProgressListener,
                                 ImageCompressionProperties imageCompressionProperties,
                                 FileProperties fileProperties) {
        this.fileInfoRepository = fileInfoRepository;
        this.uploadProgressListener = uploadProgressListener;
        this.imageCompressionProperties = imageCompressionProperties;
        this.fileProperties = fileProperties;
    }

    public void init() {
        try {
            // 验证路径安全性
            if (!FileNameSanitizer.isSafePath(fileProperties.getStorage().getUploadDir())) {
                log.warn("配置的上传目录不在安全路径内: {}", fileProperties.getStorage().getUploadDir());
                // 使用默认安全路径
                fileProperties.getStorage().setUploadDir("d:/uploads");
            }
            
            Files.createDirectories(Paths.get(fileProperties.getStorage().getUploadDir()));
            log.info("文件存储目录已创建或已存在: {}", fileProperties.getStorage().getUploadDir());
        } catch (IOException e) {
            log.error("无法创建文件存储目录: {}", fileProperties.getStorage().getUploadDir(), e);
            throw new RuntimeException("无法创建文件存储目录: " + fileProperties.getStorage().getUploadDir(), e);
        }
    }

    @Override
    public FileInfo storeFile(MultipartFile file, String accessRole, boolean isPublic) {
        List<FileInfo> files = storeFiles(List.of(file), accessRole, isPublic);
        return files.get(0);
    }

    @Override
    public List<FileInfo> storeFiles(List<MultipartFile> files, String accessRole, boolean isPublic) {
        List<FileInfo> result = new ArrayList<>();
        
        for (MultipartFile file : files) {
            // 文件验证
            if (!FileValidator.isValidFile(file, fileProperties.getStorage().getAllowedExtensions(), 
                    fileProperties.getStorage().getMaxSize())) {
                log.error("文件验证失败: {} (大小: {}KB)", file.getOriginalFilename(), file.getSize() / 1024);
                throw new RuntimeException("文件不符合要求（大小或格式）");
            }
            
            // 生成唯一且安全的文件名
            String originalFilename = FileNameSanitizer.sanitizeFileName(
                    file.getOriginalFilename());
            String fileExtension = FileValidator.getFileExtension(originalFilename);
            String storedFileName = generateUniqueFileName(fileExtension);
            
            // 保存文件
            Path targetLocation = Paths.get(fileProperties.getStorage().getUploadDir()).resolve(storedFileName);
            try {
                // 处理图片压缩
                if (isImageFile(fileExtension) && imageCompressionProperties.isEnabled()) {
                    ByteArrayOutputStream compressedOutputStream = new ByteArrayOutputStream();
                    Thumbnails.of(file.getInputStream())
                            .size(imageCompressionProperties.getWidth(), imageCompressionProperties.getHeight())
                            .outputFormat(fileExtension.replace(".", ""))
                            .outputQuality(imageCompressionProperties.getQuality())
                            .toOutputStream(compressedOutputStream);
                    
                    // 写入压缩后的文件
                    Files.write(targetLocation, compressedOutputStream.toByteArray(), StandardOpenOption.CREATE);
                } else {
                    // 监听上传进度
                    InputStream inputStream = file.getInputStream();
                    Path tempFile = Files.createTempFile("upload-", ".tmp");
                    
                    try (InputStream is = inputStream) {
                        byte[] buffer = new byte[8192];
                        int bytesRead;
                        long totalBytesRead = 0;
                        
                        while ((bytesRead = is.read(buffer)) != -1) {
                            Files.write(tempFile, buffer, 0, bytesRead, StandardOpenOption.CREATE, StandardOpenOption.APPEND);
                            totalBytesRead += bytesRead;
                            
                            // 更新上传进度
                            String uploadId = getCurrentUploadId();
                            if (uploadId != null) {
                                uploadProgressListener.updateProgress(uploadId, totalBytesRead, file.getSize());
                            }
                        }
                        
                        // 将临时文件移动到目标位置
                        Files.move(tempFile, targetLocation, StandardCopyOption.REPLACE_EXISTING);
                    } finally {
                        // 清理临时文件
                        Files.deleteIfExists(tempFile);
                    }
                }
                
                // 创建文件信息实体
                FileInfo fileInfo = new FileInfo();
                fileInfo.setFileName(storedFileName);
                fileInfo.setOriginalName(originalFilename);
                fileInfo.setFilePath(targetLocation.toString());
                fileInfo.setFileSize(file.getSize());
                fileInfo.setFileType(file.getContentType());
                fileInfo.setUploadTime(LocalDateTime.now());
                fileInfo.setAccessRole(accessRole);
                fileInfo.setPublic(isPublic);
                fileInfo.setCompressed(imageCompressionProperties.isEnabled() && isImageFile(fileExtension));
                
                // 保存到数据库
                result.add(fileInfoRepository.save(fileInfo));
                
                log.info("文件上传成功: {} (原始名称: {})", storedFileName, originalFilename);
            } catch (IOException ex) {
                log.error("文件存储失败: {}", originalFilename, ex);
                throw new RuntimeException("文件存储失败: " + originalFilename, ex);
            }
        }
        
        return result;
    }

    @Override
    public FileInfo getFileById(Long id) {
        return fileInfoRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("文件不存在，ID: " + id));
    }

    @Override
    public FileInfo getFileByFileName(String fileName) {
        return fileInfoRepository.findByFileName(fileName)
                .orElseThrow(() -> new RuntimeException("文件不存在: " + fileName));
    }

    @Override
    public void deleteFile(String fileName) {
        FileInfo fileInfo = getFileByFileName(fileName);
        deleteFileById(fileInfo.getId());
    }

    @Override
    public void deleteFileById(Long id) {
        FileInfo fileInfo = getFileById(id);
        try {
            Path filePath = Paths.get(fileInfo.getFilePath());
            Files.deleteIfExists(filePath);
            
            fileInfoRepository.deleteById(id);
            log.info("文件删除成功: {} (ID: {})", fileInfo.getFileName(), id);
        } catch (IOException ex) {
            log.error("文件删除失败: {} (ID: {})", fileInfo.getFileName(), id, ex);
            throw new RuntimeException("文件删除失败: " + fileInfo.getFileName(), ex);
        }
    }

    @Override
    public byte[] getRawFileData(String fileName) throws IOException {
        FileInfo fileInfo = getFileByFileName(fileName);        Path filePath = Paths.get(fileInfo.getFilePath());
        return Files.readAllBytes(filePath);
    }

    @Override
    public byte[] getFileForDownload(String fileName, String range) throws IOException {
        FileInfo fileInfo = getFileByFileName(fileName);
        Path filePath = Paths.get(fileInfo.getFilePath());
        
        if (range == null || range.isEmpty()) {
            // 普通下载
            return Files.readAllBytes(filePath);
        } else {
            // 断点续传
            String[] ranges = range.replace("bytes=", "").split("-");
            long start = Long.parseLong(ranges[0]);
            long end = ranges.length > 1 ? Long.parseLong(ranges[1]) : Files.size(filePath) - 1;
            
            try (InputStream inputStream = Files.newInputStream(filePath)) {
                inputStream.skip(start);
                int contentLength = (int) (end - start + 1);
                byte[] data = new byte[contentLength];
                int read = inputStream.read(data, 0, contentLength);
                
                if (read != contentLength) {
                    throw new IOException("无法读取完整的文件范围");
                }
                
                return data;
            }
        }
    }

    // 文件验证方法
    private void validateFile(MultipartFile file) {
        // 检查文件大小
        if (file.getSize() > maxFileSize) {
            throw new RuntimeException("文件大小超过限制: " + maxFileSize / 1024 / 1024 + "MB");
        }
        
        // 检查文件扩展名
        String originalFilename = StringUtils.cleanPath(file.getOriginalFilename());
        String fileExtension = getFileExtension(originalFilename);
        if (!isAllowedExtension(fileExtension)) {
            throw new RuntimeException("不允许的文件类型: " + fileExtension);
        }
        
        // 检查文件内容类型（XSS防护）
        if (!isSafeContentType(file.getContentType(), fileExtension)) {
            throw new RuntimeException("不安全的文件类型: " + file.getContentType());
        }
    }

    // 获取文件扩展名
    private String getFileExtension(String filename) {
        int dotIndex = filename.lastIndexOf(".");
        if (dotIndex == -1 || dotIndex == 0 || dotIndex == filename.length() - 1) {
            return "";
        }
        return filename.substring(dotIndex).toLowerCase();
    }

    // 检查是否允许的扩展名
    private boolean isAllowedExtension(String extension) {
        if (extension == null || extension.isEmpty()) {
            return false;
        }
        
        String[] allowedExts = allowedExtensions.split(",");
        for (String ext : allowedExts) {
            if (ext.equalsIgnoreCase(extension)) {
                return true;
            }
        }
        return false;
    }

    // 检查是否为图片文件
    private boolean isImageFile(String extension) {
        return extension != null && (extension.equalsIgnoreCase(".jpg") || 
                extension.equalsIgnoreCase(".jpeg") || 
                extension.equalsIgnoreCase(".png") || 
                extension.equalsIgnoreCase(".gif"));
    }

    // 验证内容类型安全性
    private boolean isSafeContentType(String contentType, String fileExtension) {
        if (contentType == null) {
            return false;
        }
        
        if (isImageFile(fileExtension)) {
            return contentType.startsWith("image/");
        }
        
        // 可以添加其他文件类型的检查
        return true;
    }

    // 生成唯一文件名
    private String generateUniqueFileName(String extension) {
        return UUID.randomUUID().toString() + extension;
    }

    // 获取当前请求的uploadId
    private String getCurrentUploadId() {
        return uploadProgressListener.getCurrentUploadId();
    }
}