package org.example.service;

import org.example.entity.FileInfo;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

public interface FileStorageService {
    FileInfo storeFile(MultipartFile file, String accessRole, boolean isPublic);
    List<FileInfo> storeFiles(List<MultipartFile> files, String accessRole, boolean isPublic);
    FileInfo getFileById(Long id);
    FileInfo getFileByFileName(String fileName);
    void deleteFile(String fileName);
    void deleteFileById(Long id);
    byte[] getRawFileData(String fileName) throws IOException;
    byte[] getFileForDownload(String fileName, String range) throws IOException;
}