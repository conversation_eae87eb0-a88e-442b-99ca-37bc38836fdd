package org.example.listener;

import org.example.config.UploadProgressProperties;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Component
public class UploadProgressListener {
    private final UploadProgressProperties uploadProgressProperties;
    
    // 存储上传进度信息
    private final Map<String, Long> uploadProgressMap = new ConcurrentHashMap<>();
    private final Map<String, Long> uploadSizeMap = new ConcurrentHashMap<>();
    private final ScheduledExecutorService cleanupExecutor = new ScheduledThreadPoolExecutor(1);

    public UploadProgressListener(UploadProgressProperties uploadProgressProperties) {
        this.uploadProgressProperties = uploadProgressProperties;
        
        // 启动定期清理任务
        if (uploadProgressProperties.isEnabled()) {
            cleanupExecutor.scheduleAtFixedRate(this::cleanupExpiredProgress, 
                uploadProgressProperties.getRetentionTimeMinutes(), 
                uploadProgressProperties.getRetentionTimeMinutes(), 
                TimeUnit.MINUTES);
        }
    }

    public void updateProgress(String uploadId, long bytesRead, long contentLength) {
        if (contentLength > 0 && uploadProgressProperties.isEnabled()) {
            uploadProgressMap.put(uploadId, bytesRead);
            uploadSizeMap.put(uploadId, contentLength);
        }
    }

    public void removeProgress(String uploadId) {
        uploadProgressMap.remove(uploadId);
        uploadSizeMap.remove(uploadId);
    }

    public Map<String, Object> getProgress(String uploadId) {
        Map<String, Object> progress = new HashMap<>();
        
        if (!uploadProgressProperties.isEnabled()) {
            progress.put("status", "disabled");
            return progress;
        }
        
        if (uploadSizeMap.containsKey(uploadId)) {
            long totalBytes = uploadSizeMap.get(uploadId);
            long bytesReceived = uploadProgressMap.getOrDefault(uploadId, 0L);
            int percent = totalBytes > 0 ? (int) ((bytesReceived * 100) / totalBytes) : 0;
            
            progress.put("bytesReceived", bytesReceived);
            progress.put("totalBytes", totalBytes);
            progress.put("percent", percent);
            progress.put("status", "uploading");
        } else {
            progress.put("status", "not_found");
        }
        
        return progress;
    }

    public String getCurrentUploadId() {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes != null) {
            return (String) requestAttributes.getAttribute("uploadId", RequestAttributes.SCOPE_REQUEST);
        }
        return null;
    }
    
    /**
     * 清理过期的上传进度信息
     */
    private void cleanupExpiredProgress() {
        if (!uploadProgressProperties.isEnabled()) {
            return;
        }
        
        long now = System.currentTimeMillis();
        uploadProgressMap.keySet().removeIf(key -> {
            // 这里简单地根据保留时间清理所有记录
            // 实际实现可能需要更复杂的逻辑，比如跟踪每个记录的时间戳
            return true;
        });
        uploadSizeMap.keySet().removeIf(key -> {
            return true;
        });
    }
}