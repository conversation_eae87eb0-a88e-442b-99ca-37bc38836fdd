package org.example.controller;

import org.example.listener.UploadProgressListener;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/api/progress")
public class UploadProgressController {
    private final UploadProgressListener uploadProgressListener;

    public UploadProgressController(UploadProgressListener uploadProgressListener) {
        this.uploadProgressListener = uploadProgressListener;
    }

    @GetMapping("/{uploadId}")
    public Map<String, Object> getUploadProgress(@PathVariable String uploadId) {
        return uploadProgressListener.getProgress(uploadId);
    }
}