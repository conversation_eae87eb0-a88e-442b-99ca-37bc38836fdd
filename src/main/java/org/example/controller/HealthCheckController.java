package org.example.controller;

import io.micrometer.core.instrument.MeterRegistry;
import org.example.config.FileProperties;
import org.springframework.web.bind.annotation.*;

import java.nio.file.*;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/health")
public class HealthCheckController {
    private final MeterRegistry meterRegistry;
    private final FileProperties fileProperties;

    public HealthCheckController(MeterRegistry meterRegistry, FileProperties fileProperties) {
        this.meterRegistry = meterRegistry;
        this.fileProperties = fileProperties;
    }

    @GetMapping("/storage")
    public Map<String, Object> checkStorageHealth() {
        Map<String, Object> healthInfo = new HashMap<>();
        
        try {
            Path uploadPath = Paths.get(fileProperties.getStorage().getUploadDir());
            
            // 检查目录是否存在
            boolean exists = Files.exists(uploadPath);
            healthInfo.put("exists", exists);
            
            if (exists) {
                // 获取磁盘使用情况
                FileStore fileStore = Files.getFileStore(uploadPath);
                long totalSpace = fileStore.getTotalSpace();
                long usableSpace = fileStore.getUsableSpace();
                long usedSpace = totalSpace - usableSpace;
                double usedPercentage = (double) usedSpace / totalSpace * 100;
                
                healthInfo.put("totalSpace", totalSpace);
                healthInfo.put("usableSpace", usableSpace);
                healthInfo.put("usedSpace", usedSpace);
                healthInfo.put("usedPercentage", (double) Math.round(usedPercentage * 100) / 100);
                
                // 文件数量
                long fileCount = Files.list(uploadPath).count();
                healthInfo.put("fileCount", fileCount);
                
                // 存储空间状态
                if (usedPercentage > 90) {
                    healthInfo.put("status", "CRITICAL");
                    healthInfo.put("message", "存储空间即将耗尽");
                } else if (usedPercentage > 80) {
                    healthInfo.put("status", "WARNING");
                    healthInfo.put("message", "存储空间使用率过高");
                } else {
                    healthInfo.put("status", "OK");
                    healthInfo.put("message", "存储空间使用正常");
                }
            } else {
                healthInfo.put("status", "CRITICAL");
                healthInfo.put("message", "文件存储目录不存在");
            }
            
        } catch (Exception e) {
            healthInfo.put("status", "ERROR");
            healthInfo.put("error", e.getMessage());
        }
        
        return healthInfo;
    }
}