package org.example.controller;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.extern.slf4j.Slf4j;
import org.example.dto.ApiResponse;
import org.example.dto.FileResponse;
import org.example.service.FileStorageService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/files")
@Slf4j
public class FileController {
    private final FileStorageService fileStorageService;
    private final MeterRegistry meterRegistry;

    @Value("${server.servlet.context-path}")
    private String contextPath;

    public FileController(FileStorageService fileStorageService, MeterRegistry meterRegistry) {
        this.fileStorageService = fileStorageService;
        this.meterRegistry = meterRegistry;
    }

    /**
     * 单文件上传接口
     */
    @PostMapping(path = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<ApiResponse<FileResponse>> uploadFile(
            @RequestParam("file") MultipartFile file,
            @RequestParam(name = "accessRole", required = false, defaultValue = "ROLE_USER") String accessRole,
            @RequestParam(name = "isPublic", required = false, defaultValue = "false") boolean isPublic) {
        log.info("收到文件上传请求: {} (大小: {}KB)", file.getOriginalFilename(), file.getSize() / 1024);
        
        // 增加上传计数器
        meterRegistry.counter("file.upload.count").increment();
        
        try {
            return ResponseEntity.ok(
                    ApiResponse.success(FileResponse.fromEntity(
                            fileStorageService.storeFile(file, accessRole, isPublic)
                    ))
            );
        } catch (Exception e) {
            meterRegistry.counter("file.error.count").increment();
            throw e;
        }
    }

    /**
     * 多文件上传接口
     */
    @PostMapping(path = "/uploads", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<ApiResponse<List<FileResponse>>> uploadMultipleFiles(
            @RequestParam("files") List<MultipartFile> files,
            @RequestParam(name = "accessRole", required = false, defaultValue = "ROLE_USER") String accessRole,
            @RequestParam(name = "isPublic", required = false, defaultValue = "false") boolean isPublic) {
        log.info("收到多文件上传请求: {}个文件", files.size());
        
        // 增加上传计数器（每个文件都计数）
        meterRegistry.counter("file.upload.count").increment(files.size());
        
        try {
            return ResponseEntity.ok(
                    ApiResponse.success(fileStorageService.storeFiles(files, accessRole, isPublic).stream()
                            .map(FileResponse::fromEntity)
                            .collect(Collectors.toList()))
            );
        } catch (Exception e) {
            meterRegistry.counter("file.error.count").increment();
            throw e;
        }
    }

    /**
     * 文件下载接口（支持断点续传）
     */
    @GetMapping("/download/{fileName}")
    public ResponseEntity<Resource> downloadFile(@PathVariable String fileName,
                                                  @RequestHeader(value = "Range", required = false) String range,
                                                  HttpServletRequest request) {
        try {
            // 增加下载计数器
            meterRegistry.counter("file.download.count").increment();
            
            byte[] data = fileStorageService.getFileForDownload(fileName, range);
            HttpHeaders headers = new HttpHeaders();
            
            // 设置内容类型
            String contentType = null;
            try {
                contentType = request.getServletContext().getMimeType(fileName);
            } catch (Exception ex) {
                log.error("无法获取文件MIME类型: {}", fileName, ex);
            }
            
            if (contentType == null) {
                contentType = "application/octet-stream";
            }
            headers.setContentType(MediaType.parseMediaType(contentType));
            
            // 设置内容长度
            if (range != null && !range.isEmpty()) {
                String[] ranges = range.replace("bytes=", "").split("-");
                long start = Long.parseLong(ranges[0]);
                long end = ranges.length > 1 ? Long.parseLong(ranges[1]) : data.length - 1;
                long contentLength = end - start + 1;
                
                headers.setContentLength((int) contentLength);
                headers.set(HttpHeaders.CONTENT_RANGE, "bytes " + start + "-" + end + "/" + data.length);
                
                return ResponseEntity.status(org.springframework.http.HttpStatus.PARTIAL_CONTENT)
                        .headers(headers)
                        .body(new ByteArrayResource(data));
            } else {
                headers.setContentLength(data.length);
                return ResponseEntity.ok()
                        .headers(headers)
                        .body(new ByteArrayResource(data));
            }
        } catch (IOException ex) {
            log.error("文件下载失败: {}", fileName, ex);
            meterRegistry.counter("file.error.count").increment();
            throw new RuntimeException("文件下载失败: " + fileName, ex);
        }
    }
}