package org.example.dto;

import lombok.Data;
import org.example.entity.FileInfo;

import java.time.LocalDateTime;

@Data
public class FileResponse {
    private String fileName;
    private String originalName;
    private Long fileSize;
    private String fileType;
    private LocalDateTime uploadTime;
    private String accessRole;
    private Boolean isPublic;
    private Boolean isCompressed;

    public static FileResponse fromEntity(FileInfo fileInfo) {
        FileResponse response = new FileResponse();
        response.setFileName(fileInfo.getFileName());
        response.setOriginalName(fileInfo.getOriginalName());
        response.setFileSize(fileInfo.getFileSize());
        response.setFileType(fileInfo.getFileType());
        response.setUploadTime(fileInfo.getUploadTime());
        response.setAccessRole(fileInfo.getAccessRole());
        response.setIsPublic(fileInfo.getIsPublic());
        response.setIsCompressed(fileInfo.getIsCompressed());
        return response;
    }
}