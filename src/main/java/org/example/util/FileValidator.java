package org.example.util;

import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;

public class FileValidator {
    /**
     * 验证文件是否允许上传
     */
    public static boolean isValidFile(MultipartFile file, String allowedExtensions, long maxSize) {
        // 检查文件大小
        if (file.getSize() > maxSize) {
            return false;
        }
        
        // 检查文件扩展名
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || originalFilename.isEmpty()) {
            return false;
        }
        
        String fileExtension = getFileExtension(originalFilename);
        if (!isAllowedExtension(fileExtension, allowedExtensions)) {
            return false;
        }
        
        // 检查文件内容类型（XSS防护）
        if (!isSafeContentType(file.getContentType(), fileExtension)) {
            return false;
        }
        
        return true;
    }

    /**
     * 获取文件扩展名
     */
    public static String getFileExtension(String filename) {
        int dotIndex = filename.lastIndexOf(".");
        if (dotIndex == -1 || dotIndex == 0 || dotIndex == filename.length() - 1) {
            return "";
        }
        return filename.substring(dotIndex).toLowerCase();
    }

    /**
     * 检查扩展名是否在允许的列表中
     */
    public static boolean isAllowedExtension(String extension, String allowedExtensions) {
        if (extension == null || extension.isEmpty() || allowedExtensions == null || allowedExtensions.isEmpty()) {
            return false;
        }
        
        String[] allowedExts = allowedExtensions.split(",");
        for (String ext : allowedExts) {
            if (ext.equalsIgnoreCase(extension)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 验证内容类型安全性
     */
    public static boolean isSafeContentType(String contentType, String fileExtension) {
        if (contentType == null) {
            return false;
        }
        
        if (fileExtension != null && (fileExtension.equalsIgnoreCase(".jpg") || 
                fileExtension.equalsIgnoreCase(".jpeg") || 
                fileExtension.equalsIgnoreCase(".png") || 
                fileExtension.equalsIgnoreCase(".gif"))) {
            return contentType.startsWith("image/");
        }
        
        // 可以添加其他文件类型的检查
        return true;
    }
}