package org.example.util;

import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.UUID;

public class FileNameSanitizer {
    /**
     * 生成安全的文件名
     */
    public static String sanitizeFileName(String filename) {
        if (filename == null || filename.isEmpty()) {
            return "";
        }
        
        // 获取文件扩展名
        String fileExtension = getFileExtension(filename);
        
        // 生成唯一文件名
        return UUID.randomUUID().toString() + (fileExtension.isEmpty() ? "" : "" + fileExtension);
    }

    /**
     * 获取文件扩展名
     */
    public static String getFileExtension(String filename) {
        int dotIndex = filename.lastIndexOf(".");
        if (dotIndex == -1 || dotIndex == 0 || dotIndex == filename.length() - 1) {
            return "";
        }
        return filename.substring(dotIndex).toLowerCase();
    }

    /**
     * 检查路径是否安全
     */
    public static boolean isSafePath(String path) {
        if (path == null || path.isEmpty()) {
            return false;
        }
        
        try {
            Path resolvedPath = Paths.get(path).normalize();
            Path baseDir = Paths.get(System.getProperty("user.dir"));
            
            // 确保解析后的路径在当前目录下
            return resolvedPath.startsWith(baseDir);
        } catch (Exception e) {
            return false;
        }
    }
}