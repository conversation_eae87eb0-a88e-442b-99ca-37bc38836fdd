{"groups": [{"name": "swagger", "type": "org.example.config.SwaggerProperties", "sourceType": "org.example.config.SwaggerProperties"}, {"name": "file", "type": "org.example.config.FileProperties", "sourceType": "org.example.config.FileProperties"}, {"name": "file.cleanup", "type": "org.example.config.FileCleanupProperties", "sourceType": "org.example.config.FileCleanupProperties"}, {"name": "file.upload-progress", "type": "org.example.config.UploadProgressProperties", "sourceType": "org.example.config.UploadProgressProperties"}, {"name": "image.compress", "type": "org.example.config.ImageCompressionProperties", "sourceType": "org.example.config.ImageCompressionProperties"}], "properties": [{"name": "swagger.enabled", "type": "java.lang.Bo<PERSON>an", "sourceType": "org.example.config.SwaggerProperties", "description": "是否启用Swagger", "defaultValue": true}, {"name": "swagger.title", "type": "java.lang.String", "sourceType": "org.example.config.SwaggerProperties", "description": "API文档标题", "defaultValue": "照片上传下载系统 API 文档"}, {"name": "swagger.description", "type": "java.lang.String", "sourceType": "org.example.config.SwaggerProperties", "description": "API文档描述", "defaultValue": "提供照片上传、下载、预览等核心功能的RESTful API"}, {"name": "swagger.version", "type": "java.lang.String", "sourceType": "org.example.config.SwaggerProperties", "description": "API版本号", "defaultValue": "1.0.0"}, {"name": "swagger.license", "type": "java.lang.String", "sourceType": "org.example.config.SwaggerProperties", "description": "许可证名称", "defaultValue": "Apache License 2.0"}, {"name": "swagger.license-url", "type": "java.lang.String", "sourceType": "org.example.config.SwaggerProperties", "description": "许可证URL", "defaultValue": "https://www.apache.org/licenses/LICENSE-2.0.html"}, {"name": "swagger.contact.name", "type": "java.lang.String", "sourceType": "org.example.config.SwaggerProperties", "description": "联系人名称", "defaultValue": "API Team"}, {"name": "swagger.contact.url", "type": "java.lang.String", "sourceType": "org.example.config.SwaggerProperties", "description": "联系人网址", "defaultValue": "http://example.com"}, {"name": "swagger.contact.email", "type": "java.lang.String", "sourceType": "org.example.config.SwaggerProperties", "description": "联系人邮箱", "defaultValue": "<EMAIL>"}, {"name": "file.enabled", "type": "java.lang.Bo<PERSON>an", "sourceType": "org.example.config.FileProperties", "description": "是否启用文件存储系统", "defaultValue": true}, {"name": "file.cleanup.enabled", "type": "java.lang.Bo<PERSON>an", "sourceType": "org.example.config.FileCleanupProperties", "description": "是否启用清理任务", "defaultValue": true}, {"name": "file.upload-progress.enabled", "type": "java.lang.Bo<PERSON>an", "sourceType": "org.example.config.UploadProgressProperties", "description": "是否启用上传进度跟踪", "defaultValue": true}, {"name": "image.compress.enabled", "type": "java.lang.Bo<PERSON>an", "sourceType": "org.example.config.ImageCompressionProperties", "description": "是否启用图片压缩", "defaultValue": true}], "hints": []}