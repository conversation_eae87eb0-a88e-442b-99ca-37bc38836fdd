# 启用文件存储系统
file.enabled=true

# 测试配置
file.upload-dir=target/test-uploads
file.max-size=10485760
file.allowed-extensions=.jpg,.jpeg,.png,.gif
file.cleanup.days-to-keep=1

# 文件上传进度配置
file.upload-progress.enabled=true
file.upload-progress.retention-time-minutes=30

# 文件存储健康检查配置
management.health.file-storage.enabled=true
management.health.file-storage.warning-threshold-percentage=80.0
management.health.file-storage.critical-threshold-percentage=90.0

# 文件存储健康检查端点配置
endpoint.health.file-storage.enabled=true
endpoint.health.file-storage.path=/actuator/health/storage

# H2内存数据库配置
spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1
spring.datasource.driverClassName=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=

# H2数据库控制台
spring.h2.console.enabled=true
spring.h2.console.path=/h2-console
spring.datasource.initialization-mode=always

# JPA配置
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true

# Swagger配置
swagger.enabled=true
swagger.title=照片上传下载系统 API 文档
swagger.description=提供照片上传、下载、预览等核心功能的RESTful API
swagger.version=1.0.0
swagger.license=Apache License 2.0
swagger.license-url=https://www.apache.org/licenses/LICENSE-2.0.html
swagger.contact.name=API Team
swagger.contact.url=http://example.com
swagger.contact.email=<EMAIL>

# 日志配置
logging.level.org.example=DEBUG
logging.level.org.springframework.web=WARN