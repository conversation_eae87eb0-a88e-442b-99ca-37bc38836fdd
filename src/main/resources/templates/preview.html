<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>照片预览</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
        }
        img {
            max-width: 100%;
            height: auto;
            border-radius: 4px;
            margin-top: 20px;
        }
        .info {
            margin-top: 20px;
            color: #666;
        }
    </style>
</head>
<body>
<div class="container">
    <h1>照片预览</h1>
    <img th:src="@{'/api/files/preview/' + ${fileName}}" alt="照片预览" />
    <div class="info">
        <p>文件名: <span th:text="${fileName}"></span></p>
        <p>上传时间: <span th:text="${uploadTime}"></span></p>
        <p>文件大小: <span th:text="${fileSize}"></span> KB</p>
    </div>
</div>
</body>
</html>