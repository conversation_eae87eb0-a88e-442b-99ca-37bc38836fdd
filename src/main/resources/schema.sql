-- src/main/resources/schema.sql
CREATE TABLE IF NOT EXISTS file_info (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    file_name VARCHAR(255) NOT NULL UNIQUE,
    original_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(1024) NOT NULL,
    file_size BIGINT NOT NULL,
    file_type VARCHAR(100) NOT NULL,
    upload_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_access_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_compressed BOOLEAN DEFAULT FALSE,
    access_role VARCHAR(100),
    is_public BOOLEAN DEFAULT FALSE
);

-- 索引
CREATE INDEX idx_file_name ON file_info(file_name);
CREATE INDEX idx_upload_time ON file_info(upload_time);
CREATE INDEX idx_file_type ON file_info(file_type);