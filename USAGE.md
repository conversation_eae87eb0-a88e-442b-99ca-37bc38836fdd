# 文件存储系统使用示例

## 上传文件

### 单文件上传

```bash
curl -X POST http://localhost:8080/api/files/upload \
    -H "Content-Type: multipart/form-data" \
    -F "file=@/path/to/image.jpg" \
    -F "accessRole=ROLE_USER" \
    -F "isPublic=false"
```

### 多文件上传

```bash
curl -X POST http://localhost:8080/api/files/uploads \
    -H "Content-Type: multipart/form-data" \
    -F "files[]=@/path/to/image1.jpg" \
    -F "files[]=@/path/to/image2.png" \
    -F "accessRole=ROLE_USER" \
    -F "isPublic=false"
```

## 查询上传进度

```bash
curl -X GET http://localhost:8080/api/progress/{uploadId}
```

## 下载文件

```bash
curl -X GET http://localhost:8080/api/files/download/test.jpg --output test.jpg
```

## 在线预览

```bash
curl -X GET http://localhost:8080/api/files/preview/test.jpg
```

## 获取文件信息

```bash
curl -X GET http://localhost:8080/api/files/test.jpg
```

## 删除文件

```bash
curl -X DELETE http://localhost:8080/api/files/test.jpg
```

## 获取文件下载URL

```bash
curl -X GET http://localhost:8080/api/files/url/test.jpg
```

## 存储健康检查

```bash
curl -X GET http://localhost:8080/actuator/health/storage
```

## Swagger文档

访问 http://localhost:8080/swagger-ui.html 查看API文档