<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 https://maven.apache.org/xsd/settings-1.0.0.xsd">
    <profiles>
        <profile>
            <id>spring-boot</id>
            <properties>
                <java.version>17</java.version>
                <maven.compiler.source>17</maven.compiler.source>
                <maven.compiler.target>17</maven.compiler.target>
                <encoding>UTF-8</encoding>
            </properties>
        </profile>
    </profiles>
    
    <activeProfiles>
        <activeProfile>spring-boot</activeProfile>
    </activeProfiles>
</settings>