# 贡献指南

感谢您对本项目的兴趣！以下是为项目做贡献的指导说明。

## 提交Issue

- 在提交新Issue之前，请先搜索是否已有相关讨论
- 请提供清晰的问题描述，包括复现步骤、预期行为和实际行为
- 对于Bug报告，请附上相关的日志信息和截图
- 对于功能请求，请说明使用场景和需求背景

## 提交Pull Request

### 准备工作

1. Fork仓库并创建新分支
   ```bash
   git checkout -b feature/new-feature
   ```

2. 确保代码符合编码规范
   - 使用Spring Boot推荐的代码风格
   - 添加必要的注释
   - 保持方法简洁，单个方法不超过50行
   - 类和方法应有Javadoc注释

3. 运行测试用例
   ```bash
   mvn test
   ```

### PR要求

1. 保持提交信息清晰明了
   - 使用Conventional Commits格式（feat: add new feature）
   - 描述修改内容和原因

2. 包含必要的测试用例
   - 单元测试覆盖率不低于80%
   - 集成测试覆盖主要使用场景

3. 更新文档
   - 修改API文档（如有需要）
   - 更新README.md或其他相关文档

4. 确保CI构建通过

## 开发环境搭建

1. 安装Java 17
2. 安装Maven 3.6+
3. 克隆仓库
   ```bash
   git clone https://github.com/yourname/demo2.git
   ```
4. 导入IDE（支持IntelliJ IDEA和Eclipse）
5. 配置application.properties中的开发环境参数

## 代码审查标准

1. 功能实现是否满足需求
2. 是否遵循编码规范
3. 是否有足够的测试覆盖
4. 是否影响现有功能
5. 文档是否更新
6. 是否存在潜在的安全风险

## 版本发布流程

1. 确认所有测试通过
2. 更新CHANGELOG.md
3. 打标签
   ```bash
   git tag v1.0.1
   ```
4. 推送标签
   ```bash
   git push origin v1.0.1
   ```
5. 发布到Maven中央仓库（需权限）