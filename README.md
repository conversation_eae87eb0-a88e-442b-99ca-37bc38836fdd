# 照片上传下载系统

这是一个基于Spring Boot的完整照片上传下载解决方案，提供全面的功能和良好的性能。

## 项目结构

- `starter/` - Spring Boot Starter模块，包含可重用的核心功能
- `pom.xml` - Maven项目配置
- `src/` - 主应用程序源代码
- `USAGE.md` - 使用示例文档

## 核心功能

### 上传功能
- 支持单文件和多文件上传
- 实现文件类型验证（仅允许图片格式）
- 文件大小限制（最大10MB）
- 自动生成唯一文件名
- 上传进度显示

### 下载功能
- 文件下载接口
- 支持断点续传
- 在线预览功能
- 访问权限控制

### 安全特性
- 文件类型安全检查
- 文件名安全处理
- 访问权限控制
- 防盗链措施
- XSS防护

### 性能优化
- 文件上传进度监控
- 大文件处理优化
- 图片压缩功能
- 缓存机制

### 异常处理
- 完善的异常处理机制
- 友好的错误提示
- 详细的日志记录

### 接口设计
- RESTful API设计
- 清晰的接口文档
- 标准的响应格式

## 技术栈

- Spring Boot 最新稳定版本
- Spring Web
- Spring Data JPA
- Spring Security
- Spring Actuator
- Swagger2 (API文档)
- Thumbnailator (图片处理)
- Lombok
- Micrometer (指标收集)

## 快速开始

1. 启动应用
```bash
mvn spring-boot:run
```

2. 访问Swagger文档
http://localhost:8080/swagger-ui.html

3. 查看使用示例
请参考[USAGE.md](USAGE.md)文件中的详细说明

## 模块化架构
本项目采用模块化架构，核心功能封装在`starter/`目录下的Spring Boot Starter模块中，方便复用和维护。