# 项目部署文档

## 部署方式

本项目支持以下几种部署方式：

### 1. 本地Maven部署

```bash
# 构建项目
mvn clean package

# 运行项目
java -jar target/demo2-0.0.1-SNAPSHOT.jar
```

### 2. Docker部署

```bash
# 构建并启动容器
docker-compose up -d
```

### 3. 手动部署到Servlet容器（如Tomcat）

1. 将项目打包为WAR文件
2. 将WAR文件复制到Tomcat的webapps目录
3. 启动Tomcat服务器

### 4. 使用Windows脚本部署

```bat
:: 构建项目
build.bat

:: 运行项目
run.bat
```

## 系统要求

- Java 17 或更高版本
- Maven 3.6+ (用于构建)
- 至少2GB内存
- 至少5GB磁盘空间（用于文件存储）

## 数据库配置

本项目默认使用H2内存数据库，适用于开发和测试环境。生产环境建议改为MySQL或PostgreSQL。

在application.properties中修改数据库配置：

```properties
# MySQL配置示例
spring.datasource.url=***********************************************************************
spring.datasource.username=root
spring.datasource.password=yourpassword
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# Hibernate配置
spring.jpa.hibernate.ddl-auto=update
```

## 文件存储路径配置

在application.properties中修改文件存储路径：

```properties
file.storage.upload-dir=d:/uploads
```

请确保运行时账户有该目录的读写权限。

## 安全配置

1. 修改默认的安全配置（在application.properties中）：
   ```properties
   spring.security.user.name=admin
   spring.security.user.password=your-secure-password
   ```

2. 生产环境建议启用HTTPS：
   ```properties
   server.port=8443
   server.ssl.key-store=classpath:keystore.p12
   server.ssl.key-store-password=your-keystore-password
   server.ssl.key-store-type=PKCS12
   server.ssl.key-alias=localhost
   ```

## 监控和维护

1. 访问健康检查端点：`/actuator/health/storage`
2. 查看H2数据库控制台：`/h2-console`
3. 查看指标数据：`/actuator/metrics`

## 常见问题

### 上传大文件失败

调整以下配置参数：
```properties
file.storage.max-size=52428800  # 设置为50MB或其他合适值
spring.servlet.multipart.max-file-size=50MB
spring.servlet.multipart.max-request-size=100MB
```

### 文件存储目录不可写

1. 检查目录是否存在
2. 检查目录权限设置
3. 在启动命令中添加日志输出：`--debug`

### 性能优化建议

1. 启用图片压缩功能：`image.compress.enabled=true`
2. 调整连接池大小：
   ```properties
   spring.datasource.hikari.maximum-pool-size=10
   spring.datasource.hikari.minimum-idle=5
   ```
3. 启用缓存：`spring.cache.type=REDIS`