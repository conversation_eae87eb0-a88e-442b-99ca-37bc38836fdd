version: '3'
services:
  file-storage-service:
    build: .
    container_name: file-storage-service
    ports:
      - "8080:8080"
    volumes:
      - uploads:/app/uploads
    environment:
      SPRING_DATASOURCE_URL: jdbc:h2:mem:filestorage;DB_CLOSE_DELAY=-1
      SPRING_DATASOURCE_USERNAME: sa
      SPRING_DATASOURCE_PASSWORD:
      SPRING_JPA_HIBERNATE_DDL-AL_AUTO: update
    restart: always

volumes:
  uploads: