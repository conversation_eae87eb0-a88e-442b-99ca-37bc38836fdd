package org.example.starter.autoconfigure;

import org.example.config.FileProperties;
import org.example.config.FileStorageAutoConfiguration;
import org.springframework.boot.autoconfigure.AutoConfigurationImportSelector;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.*;

@Configuration
@ConditionalOnClass(FileProperties.class)
@AutoConfigureAfter({FileStorageAutoConfiguration.class})
@Import({FileStorageAutoConfiguration.class})
public class FileStorageStarterAutoConfiguration {
}