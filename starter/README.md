# 文件存储系统 Starter 模块

这是一个Spring Boot Starter模块，提供了照片上传下载系统的通用功能，包括：

- 文件上传下载功能
- 图片压缩处理
- 上传进度跟踪
- 文件存储管理
- 存储健康检查
- 自动清理任务
- 安全防护措施

## 使用说明

### 添加依赖

在您的Spring Boot项目的pom.xml中添加以下依赖：

```xml
<dependency>
    <groupId>org.example</groupId>
    <artifactId>file-storage-starter</artifactId>
    <version>1.0.0</version>
</dependency>
```

### 配置参数

在application.properties或application.yml中配置文件存储相关参数：

```properties
# 文件存储配置
file.enabled=true
file.storage.upload-dir=d:/uploads
file.storage.allowed-extensions=.jpg,.jpeg,.png,.gif
file.storage.max-size=10485760  # 10MB

# 文件清理配置
file.cleanup.enabled=true
file.cleanup.days-to-keep=30
file.cleanup.storage-usage-warning-threshold=80.0

# 图片压缩配置
image.compress.enabled=true
image.compress.quality=0.7
image.compress.width=800
image.compress.height=600

# 上传进度跟踪配置
file.upload-progress.enabled=true
file.upload-progress.retention-time-minutes=30

# 文件存储健康检查配置
management.health.file-storage.enabled=true
management.health.file-storage.warning-threshold-percentage=80.0
management.health.file-storage.critical-threshold-percentage=90.0
endpoint.health.file-storage.enabled=true
endpoint.health.file-storage.path=/actuator/health/storage
```

### 启用功能

该模块会自动启用以下功能：

- 文件上传下载接口
- 图片压缩处理
- 文件存储健康检查
- 上传进度跟踪
- 自动清理任务

### 使用API

模块提供以下RESTful API：

#### 上传接口
- `POST /api/files/upload` - 单文件上传
- `POST /api/files/uploads` - 多文件上传

#### 下载接口
- `GET /api/files/download/{fileName}` - 文件下载（支持断点续传）
- `GET /api/files/preview/{fileName}` - 在线预览

#### 文件信息接口
- `GET /api/files/{fileName}` - 获取文件元数据
- `DELETE /api/files/{fileName}` - 删除文件
- `GET /api/files/url/{fileName}` - 获取文件下载URL

#### 进度查询接口
- `GET /api/progress/{uploadId}` - 查询上传进度

#### 健康检查接口
- `GET /actuator/health/storage` - 存储健康状态检查（需要启用management端点）

## 功能开关

您可以通过配置文件中的属性来启用或禁用特定功能：

- `file.enabled=false` - 禁用整个文件存储系统
- `file.cleanup.enabled=false` - 禁用文件清理任务
- `image.compress.enabled=false` - 禁用图片压缩功能
- `file.upload-progress.enabled=false` - 禁用上传进度跟踪
- `management.health.file-storage.enabled=false` - 禁用存储健康检查
- `endpoint.health.file-storage.enabled=false` - 禁用存储健康检查端点

## 版本历史

### v1.0.0
- 初始版本，包含完整的文件存储解决方案
- 支持上传、下载、压缩、安全防护、健康检查等功能